PORTFOLIO WEBSITE - GENERATED BY PORTFOLIOAI

Thank you for using PortfolioAI to create your professional portfolio!

== CONTENTS ==

This ZIP file contains a complete, ready-to-use portfolio website with the following files:

1. index.html - The main HTML file for your portfolio
2. styles.css - The CSS stylesheet for your portfolio design
3. script.js - JavaScript for interactive elements
4. README.txt - This file with instructions

== HOW TO USE ==

To view your portfolio locally:
1. Extract all files from this ZIP to a folder on your computer
2. Double-click the index.html file to open it in your web browser

To publish your portfolio online:
1. Upload all files to a web hosting service of your choice
   (GitHub Pages, Netlify, Vercel, or any web hosting provider)
2. Your portfolio will be accessible at the URL provided by your hosting service

== CUSTOMIZATION ==

You can customize your portfolio by editing the files:

- To change content: Edit the index.html file
- To change styles: Edit the styles.css file
- To change behavior: Edit the script.js file

== SUPPORT ==

If you need help with your portfolio or want to create a more advanced version,
visit PortfolioAI at https://lovable.dev/projects/5ce5031b-9469-4274-9655-97abf5b28b9c

== LICENSE ==

This portfolio is provided for your personal use. You are free to modify and use it
for your personal or professional purposes.

Created with ♥ by PortfolioAI
