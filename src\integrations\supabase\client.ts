// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://wdwanegbbitpyatahrny.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indkd2FuZWdiYml0cHlhdGFocm55Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0ODEwNTIsImV4cCI6MjA2MzA1NzA1Mn0.GlWfm-aEDakJTYqM4PNhZijlDir_ECNPMtM-Ze4JQIQ";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);